/**
 * WebSocket组合式函数
 * 提供WebSocket连接管理和消息处理功能
 */

import { ref, reactive, onUnmounted } from 'vue'

export function useWebSocket() {
  const socket = ref(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 3000

  // 消息处理器映射
  const messageHandlers = reactive(new Map())
  
  // 连接统计
  const stats = reactive({
    totalConnections: 0,
    messagesSent: 0,
    messagesReceived: 0,
    lastConnected: null,
    lastDisconnected: null
  })

  /**
   * 获取WebSocket连接URL
   */
  function getWebSocketUrl() {
    // 在开发环境下，直接连接到后端服务器
    if (import.meta.env.DEV) {
      return 'ws://localhost:8001/ws'
    }

    // 生产环境使用相对路径
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    return `${protocol}//${window.location.host}/ws`
  }

  /**
   * 连接WebSocket
   */
  async function connect() {
    if (isConnected.value || isConnecting.value) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        isConnecting.value = true

        // 构建WebSocket URL
        const wsUrl = getWebSocketUrl()
        console.log('尝试连接WebSocket:', wsUrl)
        console.log('当前环境:', import.meta.env.MODE)

        socket.value = new WebSocket(wsUrl)

        socket.value.onopen = () => {
          isConnected.value = true
          isConnecting.value = false
          reconnectAttempts.value = 0
          stats.totalConnections++
          stats.lastConnected = new Date().toISOString()
          
          console.log('WebSocket连接已建立')
          resolve()
        }

        socket.value.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            stats.messagesReceived++
            
            // 调用对应的消息处理器
            const handler = messageHandlers.get(data.type)
            if (handler) {
              handler(data.data || data)
            }
            
            // 调用通用消息处理器
            const generalHandler = messageHandlers.get('*')
            if (generalHandler) {
              generalHandler(data)
            }
            
          } catch (error) {
            console.error('WebSocket消息解析失败:', error)
          }
        }

        socket.value.onclose = (event) => {
          isConnected.value = false
          isConnecting.value = false
          stats.lastDisconnected = new Date().toISOString()

          console.log('WebSocket连接已关闭:', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
            url: wsUrl,
            environment: import.meta.env.MODE
          })

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
            setTimeout(() => {
              reconnectAttempts.value++
              console.log(`尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts}) 到 ${wsUrl}`)
              connect().catch(error => {
                console.error('重连失败:', error)
              })
            }, reconnectDelay)
          } else if (reconnectAttempts.value >= maxReconnectAttempts) {
            console.error('已达到最大重连次数，停止重连')
          }
        }

        socket.value.onerror = (error) => {
          console.error('WebSocket连接错误:', {
            error,
            url: wsUrl,
            environment: import.meta.env.MODE,
            readyState: socket.value?.readyState
          })
          isConnecting.value = false
          reject(new Error(`WebSocket连接失败: ${wsUrl}`))
        }

        // 连接超时处理
        setTimeout(() => {
          if (isConnecting.value) {
            socket.value?.close()
            isConnecting.value = false
            reject(new Error('WebSocket连接超时'))
          }
        }, 10000)

      } catch (error) {
        isConnecting.value = false
        reject(error)
      }
    })
  }

  /**
   * 断开WebSocket连接
   */
  function disconnect() {
    if (socket.value) {
      socket.value.close(1000, '主动断开连接')
      socket.value = null
    }
    isConnected.value = false
    isConnecting.value = false
    reconnectAttempts.value = 0
  }

  /**
   * 发送消息
   */
  async function sendMessage(message) {
    if (!isConnected.value) {
      throw new Error('WebSocket未连接')
    }

    try {
      const messageStr = JSON.stringify(message)
      socket.value.send(messageStr)
      stats.messagesSent++
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      throw error
    }
  }

  /**
   * 注册消息处理器
   */
  function onMessage(type, handler) {
    messageHandlers.set(type, handler)
    
    // 返回取消注册的函数
    return () => {
      messageHandlers.delete(type)
    }
  }

  /**
   * 移除消息处理器
   */
  function offMessage(type) {
    messageHandlers.delete(type)
  }

  /**
   * 发送ping消息
   */
  async function ping() {
    return sendMessage({ type: 'ping' })
  }

  /**
   * 获取系统状态
   */
  async function getStatus() {
    return sendMessage({ type: 'get_status' })
  }

  /**
   * 启动预览流
   */
  async function startPreview() {
    return sendMessage({ type: 'start_preview' })
  }

  /**
   * 停止预览流
   */
  async function stopPreview() {
    return sendMessage({ type: 'stop_preview' })
  }

  /**
   * 获取截图统计
   */
  async function getScreenshotStats() {
    return sendMessage({ type: 'get_screenshot_stats' })
  }

  /**
   * 获取截图历史
   */
  async function getScreenshotHistory(limit = 50) {
    return sendMessage({ 
      type: 'get_screenshot_history',
      limit 
    })
  }

  /**
   * 获取WebSocket统计
   */
  async function getWebSocketStats() {
    return sendMessage({ type: 'get_websocket_stats' })
  }

  /**
   * 设置心跳检测
   */
  function setupHeartbeat(interval = 30000) {
    const heartbeatTimer = setInterval(() => {
      if (isConnected.value) {
        ping().catch(console.error)
      }
    }, interval)

    // 返回清理函数
    return () => {
      clearInterval(heartbeatTimer)
    }
  }

  /**
   * 等待连接建立
   */
  function waitForConnection(timeout = 10000) {
    return new Promise((resolve, reject) => {
      if (isConnected.value) {
        resolve()
        return
      }

      const timer = setTimeout(() => {
        reject(new Error('等待连接超时'))
      }, timeout)

      const checkConnection = () => {
        if (isConnected.value) {
          clearTimeout(timer)
          resolve()
        } else {
          setTimeout(checkConnection, 100)
        }
      }

      checkConnection()
    })
  }

  // 组件卸载时自动断开连接
  onUnmounted(() => {
    disconnect()
  })

  return {
    // 状态
    socket,
    isConnected,
    isConnecting,
    reconnectAttempts,
    stats,

    // 连接管理
    connect,
    disconnect,
    waitForConnection,

    // 消息处理
    sendMessage,
    onMessage,
    offMessage,

    // 便捷方法
    ping,
    getStatus,
    startPreview,
    stopPreview,
    getScreenshotStats,
    getScreenshotHistory,
    getWebSocketStats,

    // 工具方法
    setupHeartbeat
  }
}
