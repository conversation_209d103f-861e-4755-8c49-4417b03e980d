2025-07-31 08:36:46 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [3036] using StatReload
2025-07-31 08:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [24796]
2025-07-31 08:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-31 08:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64188 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-31 08:36:53 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-31 08:36:53 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65260 - "WebSocket /ws" [accepted]
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65262 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65264 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65267 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65269 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65270 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65274 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65280 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65282 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65285 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65297 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65295 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65294 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65304 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65306 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65356 - "WebSocket /ws" [accepted]
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65359 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65365 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65371 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65373 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65374 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65390 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65391 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65389 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65397 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65407 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65405 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65406 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65412 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65416 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65503 - "WebSocket /ws" [accepted]
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65506 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65512 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65516 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65518 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65521 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65532 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65531 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65533 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49164 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49166 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49165 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49173 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49178 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49180 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:42:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49337 - "WebSocket /ws" [accepted]
2025-07-31 08:43:58 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49649 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:44:09 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49710 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:44:09 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49710 - "GET /favicon.ico HTTP/1.1" 404 Not Found
2025-07-31 08:45:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50036 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50150 - "WebSocket /ws" [accepted]
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50153 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50154 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50161 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50162 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50164 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50163 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50186 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50190 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50187 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50189 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50191 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50192 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50198 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50200 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50555 - "WebSocket /ws" [accepted]
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50558 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50559 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50572 - "WebSocket /ws" [accepted]
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50574 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50576 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50580 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50581 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50582 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50586 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50592 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50594 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50599 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50608 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50614 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50621 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50625 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50627 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:47:45 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50675 - "WebSocket /ws" [accepted]
2025-07-31 08:48:01 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50735 - "WebSocket /ws" [accepted]
2025-07-31 08:48:10 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50784 - "WebSocket /ws" [accepted]
2025-07-31 08:48:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50861 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:49:57 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:106 - 后端端口 8001 可用
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8001 --reload
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8001/health
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8001 (Press CTRL+C to quit)
2025-07-31 08:49:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [13108] using StatReload
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [9032]
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51234 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8001/health
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-31 08:50:05 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-31 08:50:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51408 - "WebSocket /ws" [accepted]
2025-07-31 08:50:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51410 - "WebSocket /ws" [accepted]
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51412 - "WebSocket /ws" [accepted]
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51416 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51415 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51419 - "WebSocket /ws" [accepted]
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51423 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51422 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51426 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51428 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51429 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51436 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51441 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51443 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51448 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51458 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51460 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51465 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51472 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51474 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:50:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51540 - "WebSocket /ws" [accepted]
2025-07-31 08:50:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51545 - "WebSocket /ws" [accepted]
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51602 - "WebSocket /ws" [accepted]
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51605 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51606 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51610 - "WebSocket /ws" [accepted]
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51612 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:51:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51614 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51627 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51629 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51630 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51642 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51649 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51652 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51651 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51658 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51665 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51667 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51668 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51674 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:51:12 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51719 - "WebSocket /ws" [accepted]
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51781 - "WebSocket /ws" [accepted]
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51783 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:51:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51785 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:52:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52053 - "WebSocket /ws" [accepted]
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52055 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52057 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52067 - "WebSocket /ws" [accepted]
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52071 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52072 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52073 - "WebSocket /ws" [accepted]
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52076 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52077 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52079 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52081 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52083 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52087 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52097 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52098 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52100 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52112 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52114 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52115 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52123 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-31 08:52:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52127 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-31 17:02:10 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-31 17:02:10 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-31 17:02:10 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-31 17:02:10 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-31 17:03:58 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-31 17:03:58 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [27228] using StatReload
2025-07-31 17:04:01 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [4712]
2025-07-31 17:04:01 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-31 17:04:01 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-31 17:04:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50687 - "GET /health HTTP/1.1" 200 OK
2025-07-31 17:04:02 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-31 17:04:02 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-31 17:04:02 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-31 17:04:04 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-31 17:04:04 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50751 - "WebSocket /ws" [accepted]
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50753 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50755 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50761 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50763 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50764 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50765 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50782 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50785 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50783 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50784 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50786 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:11 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50787 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:04:12 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50792 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 304 Not Modified
2025-07-31 17:05:22 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-31 17:05:22 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-31 17:05:22 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-31 17:05:22 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-31 17:05:41 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-31 17:05:41 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [20508] using StatReload
2025-07-31 17:05:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [6836]
2025-07-31 17:05:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-31 17:05:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-31 17:05:45 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51048 - "GET /health HTTP/1.1" 200 OK
2025-07-31 17:05:45 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-31 17:05:45 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-31 17:05:45 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-31 17:05:47 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-31 17:05:47 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-31 17:05:51 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-31 17:05:51 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-31 17:05:51 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-31 17:05:51 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-31 17:06:12 - GakumasuBot - ERROR - gui.py:227 - 前端服务意外退出
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:146 - Backend: forrtl: error (200): program aborting due to window-CLOSE event
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:146 - Backend: Image              PC                Routine            Line        Source
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:146 - Backend: KERNELBASE.dll     00007FFF2BF14987  Unknown               Unknown  Unknown
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:146 - Backend: KERNEL32.DLL       00007FFF2D11259D  Unknown               Unknown  Unknown
2025-07-31 17:06:12 - GakumasuBot - INFO - gui.py:146 - Backend: ntdll.dll          00007FFF2EC6AF38  Unknown               Unknown  Unknown
2025-07-31 17:20:36 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-31 17:20:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [15340] using StatReload
2025-07-31 17:20:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [13192]
2025-07-31 17:20:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-31 17:20:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-31 17:20:40 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:54159 - "GET /health HTTP/1.1" 200 OK
2025-07-31 17:20:40 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-31 17:20:40 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-31 17:20:40 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-31 17:20:42 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-31 17:20:42 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-31 17:21:00 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-31 17:21:00 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-31 17:21:00 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-31 17:21:00 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
